[tool.poetry]
name = "nonebot-plugin-multi-source-daily"
version = "0.3.0"
description = "一个聚合多种日报源的NoneBot2插件，支持定时发送和多API源"
authors = ["webjoin111 <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/webjoin111/nonebot-plugin-multi-source-daily"
repository = "https://github.com/webjoin111/nonebot-plugin-multi-source-daily"
keywords = ["nonebot", "nonebot2", "daily", "news", "aggregator"]
packages = [{include = "nonebot_plugin_multi_source_daily"}]
exclude = [
    "**/_gsdata_/**",
    "**/__pycache__/**",
    "**/.*",
    "change/**",
    "assets/**",
    "dist/**",
    "build/**"
]

[tool.poetry.dependencies]
python = "^3.8"
nonebot2 = ">=2.3.0"
nonebot-adapter-onebot = "^2.0.0"
nonebot-plugin-alconna = ">=0.30.0"
nonebot-plugin-apscheduler = ">=0.3.0"
nonebot-plugin-htmlrender = ">=0.2.0"
nonebot-plugin-localstore = ">=0.4.0"
httpx = ">=0.23.0,<1.0.0"
# Pillow 是可选依赖，用于图片优化

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
black = "^23.0.0"
isort = "^5.10.0"

[tool.poetry.group.image.dependencies]
Pillow = ">=9.0.0,<10.0.0"

[tool.poetry.extras]
image = ["Pillow"]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.nonebot]
adapters = [
    { name = "OneBot V11", module_name = "nonebot.adapters.onebot.v11" }
]
plugins = []
plugin_dirs = []

[tool.black]
line-length = 88
target-version = ["py38"]

[tool.isort]
profile = "black"
line_length = 88
