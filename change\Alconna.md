好的，根据你提供的 `Alconna.md` 文件内容，我将为你整理一份详细且去除冗余的 `nonebot-plugin-alconna` 及 `Alconna` 核心库的文档说明。

---

# NoneBot Alconna 插件与 Alconna 核心库文档

本文档整合了 `nonebot-plugin-alconna` 插件及其核心依赖库 `Alconna` 的关键概念和用法。建议优先阅读 [Alconna 本体](#alconna-本体) 部分以理解核心解析机制。

## Alconna 本体

[`Alconna`](https://github.com/ArcletProject/Alconna) 是一个简单、灵活、高效的命令参数解析器，隶属于 `ArcletProject`，它不局限于解析命令式字符串。

### 核心概念示例

```python
from arclet.alconna import Alconna, Args, Subcommand, Option

# 定义一个类似 pip install 的命令结构
alc = Alconna(
    "pip", # 命令名
    Subcommand( # 子命令 "install"
        "install",
        Args["package", str], # 位置参数 "package"，需要字符串类型
        Option("-r|--requirement", Args["file", str]), # 选项 "-r" 或 "--requirement"，需要字符串参数 "file"
        Option("-i|--index-url", Args["url", str]),   # 选项 "-i" 或 "--index-url"，需要字符串参数 "url"
    )
)

# 解析命令字符串
res = alc.parse("pip install nonebot2 -i URL")

# 打印解析结果对象 Arparma
print(res)
# 输出: matched=True, header_match=(...), subcommands={'install': (...)}, other_args={'package': 'nonebot2', 'url': 'URL'}

# 获取所有匹配到的参数值
print(res.all_matched_args)
# 输出: {'package': 'nonebot2', 'url': 'URL'}
```

### Alconna 命令组成

#### 1. 命令头 (Header)

命令头由可选的 **前缀 (Prefix)** 和必需的 **命令名 (Command Name)** 组成。

*   **前缀与命令名**: 可以是字符串、特定类型、正则表达式、列表（表示或）、元组（表示顺序）等。
*   **类型/正则匹配**: 支持直接使用类型（如 `int`）或 `re:` 标记的正则表达式进行匹配。
*   **Bracket Header**: 类似 f-string 的语法 `cmd{name:type}`，用于在命令名中直接提取参数，如 `.rd{roll:int}`。

**命令头类型总结表:**
|前缀|命令名|匹配内容|说明|
|:-:|:-:|:-:|:-:|
|-|"foo"|`"foo"`|无前缀的纯文字头|
|-|123|`123`|无前缀的元素头|
|-|"re:\d{2}"|`"32"`|无前缀的正则头|
|-|int|`123` 或 `"456"`|无前缀的类型头|
|[int, bool]|-|`True` 或 `123`|无名的元素类头|
|["foo", "bar"]|-|`"foo"` 或 `"bar"`|无名的纯文字头|
|["foo", "bar"]|"baz"|`"foobaz"` 或 `"barbaz"`|纯文字头|
|[int, bool]|"foo"|`[123, "foo"]` 或 `[False, "foo"]`|类型头|
|[123, 4567]|"foo"|`[123, "foo"]` 或 `[4567, "foo"]`|元素头|
|[nepattern.NUMBER]|"bar"|`[123, "bar"]` 或 `[123.456, "bar"]`|表达式头|
|[123, "foo"]|"bar"|`[123, "bar"]` 或 `"foobar"` 或 `["foo", "bar"]`|混合头|
|[(int, "foo"), (456, "bar")]|"baz"|`[123, "foobaz"]` 或 `[456, "foobaz"]` 或 `[456, "barbaz"]`|对头|

**注意**: 正则只在命令名上生效，前缀中的正则会被转义。

#### 2. 参数声明 (Args)

`Args` 用于按顺序声明命令的位置参数。

**构造方式**:

*   `Args[key, var, default][key1, var1, default1][...]`
*   `Args.key[var, default]`

**组成**:

*   **`key` (str)**: 参数的名称，用于在结果中标识。
    *   **标识符**:
        *   `?` (可选): `Args["param?", str]` 或 `Args.param[str, None]`。如果匹配失败，会跳过该参数。
        *   `!` (排除): `Args["param!", int]` 匹配非整数；`Args["param!", Literal['a', 'b']]` 匹配非 'a' 且非 'b'。
        *   `/` (隐藏类型): 参数的类型注解在帮助信息中隐藏。
    *   **注释**: 使用 `#` 分隔，如 `key#注释;?` 或 `key?#注释`。
    *   **注意**: `Args` 的 `key` **不** 作为命令解析的一部分输入，除非是 `KeyWordVar`。若需 `cmd --key value` 形式，应使用 `Option`。
*   **`var` (类型/模式)**: 定义参数期望的类型和验证规则，基于 `nepattern`。
    *   **内置类型**: `str`, `int`, `float`, `bool`, `hex`, `url`, `email`, `ipv4`, `list`, `dict`, `datetime`, `Any`, `AnyString`, `Number`。
    *   **Typing**: `Literal[...]`, `Union[..., ...]`, `Optional[T]` (自动设默认值为 None), `List[T]`, `Dict[K, V]` 等。
    *   **特殊标记**:
        *   `"foo"`: 精确匹配字符串 "foo"。
        *   `RawStr("foo")`: 同上，防止被 `BasePattern` 替换。
        *   `"foo|bar|baz"`: 匹配 "foo"、"bar"、"baz" 之一。
        *   `[val1, Type1, ...]`: 匹配列表中的任意值或类型。
        *   `Callable[[X], Y]`: 匹配 X 类型的值，并用该函数处理得到 Y 类型结果。
        *   `"re:pattern"`: 正则匹配，返回第一个捕获组 `Match[0]`。
        *   `"rep:pattern"`: 正则匹配，返回 `re.Match` 对象。
        *   `{key1: val1, key2: val2, ...: fallback}`: 匹配字典中的键，返回对应的值；`...` 键匹配任意输入并返回 `fallback` 值。
    *   **`MultiVar(var, min=1, max=inf)`**: 允许多个值匹配同一个 `var` 类型，类似 `*args`。
    *   **`KeyWordVar(var)`**: 声明一个关键字参数，解析时需要 `key=value` 形式，类似 `*` 后的参数。
    *   **`MultiVar(KeyWordVar(var))`**: 允许多个 `key=value` 形式的参数，类似 `**kwargs`。
*   **`default` (Any | Field)**: 参数的默认值。
    *   可以直接提供值。
    *   使用 `Field` 可以附加更多信息：`Field(default=..., alias=..., completion=Callable, unmatch_tips=Callable, missing_tips=Callable)`。

#### 3. 选项 (Option) 和 子命令 (Subcommand)

用于定义命令的可选部分或分支。

**通用参数**:

*   **`name` 与 `aliases`**: 定义名称和别名。
    *   `Option("-p|--prompt", ...)` 或 `Option("--prompt", alias=["-p"], ...)`。
    *   别名之间用 `|` 分隔，**两侧不能有空格**。
    *   最长的名称/别名会被视为主名称。
    *   Option 的名称/别名**不强制**要求以 `-` 开头。
*   **`args` (Args)**: 定义该选项/子命令内部需要的参数。
*   **`help_text` (str)**: 帮助信息。
*   **`dest` (str)**: 在解析结果中使用的名称，默认为主名称。
*   **`requires` (list[str])**: 定义前置序列。只有当命令中按顺序出现 `requires` 中的所有字符串时，该选项/子命令才会被解析。
    ```python
    # 命令需为 "test foo bar baz qux <a:int>"
    Alconna("test", Option("qux", Args.a[int], requires=["foo", "bar", "baz"]))
    ```
*   **`default`**: 当选项/子命令未被匹配时使用的默认值。可以使用 `OptionResult` 或 `SubcommandResult` 来提供包含默认参数的复杂默认值。

**Option 特有**:

*   **`action` (Action | Callable)**: 定义匹配到该选项时的行为。
    *   **`store` (默认)**: 存储值，无 Args 时存 `Ellipsis`，有 Args 时后续覆盖。
    *   **`append`**: 追加值到列表，无 Args 时存 `Ellipsis`。
    *   **`count`**: 计数器加一，无 Args 时表现同 `store`。
    *   **预设 Actions**: `store_value`, `store_true`, `store_false`, `append_value`。

**Subcommand 特有**:

*   可以嵌套定义自己的 `Option` 和 `Subcommand`。

#### 4. 解析结果 (Arparma)

`Alconna.parse(message)` 返回 `Arparma` 对象，包含解析信息。

**主要属性**:

*   `matched` (bool): 是否成功匹配。
*   `error_info` (Exception | str): 失败时的错误信息。
*   `origin` (Any): 原始输入消息。
*   `header_match`: 命令头匹配结果。
*   `main_args` (dict): 主命令的位置参数结果。
*   `options` (dict): 所有选项的解析结果 (`OptionResult`)。
*   `subcommands` (dict): 所有子命令的解析结果 (`SubcommandResult`)。
*   `all_matched_args` (dict): 所有 Args 参数（包括主参数和选项/子命令内的）的 `{key: value}` 集合。

**`query(path: str, default: Any = None) -> Any` / `query[T](path: str, default: T = None) -> T`**:
便捷的查询方法，根据路径字符串 `path` 查找结果。

*   `"args"`: 返回 `all_matched_args`。
*   `"main_args"`, `"options"`, `"subcommands"`: 返回对应属性。
*   `"main_args.key"`, `"options.opt_name"`, `"subcommands.sub_name"`: 返回字典中的值。
*   `"args.key"`: 返回 `all_matched_args` 中的值。
*   `"opt_name"`: 返回 `options['opt_name']` (OptionResult)。
*   `"opt_name.value"`: 返回选项 `opt_name` 的值。
*   `"opt_name.args"`: 返回选项 `opt_name` 的 Args 参数字典。
*   `"opt_name.args.arg_key"` 或 `"opt_name.arg_key"`: 返回选项 `opt_name` 的参数 `arg_key` 的值。

#### 5. 元数据 (CommandMeta)

通过 `Alconna(..., meta=CommandMeta(...))` 设置命令级别的配置。

**常用属性**:

*   `description` (str): 命令描述。
*   `usage` (str): 命令用法。
*   `example` (str): 命令示例。
*   `fuzzy_match` (bool): 是否开启模糊匹配 (默认 False)。
*   `compact` (bool): 是否允许紧凑模式 (命令名/选项名后直接跟参数，无需分隔符，默认 False)。
*   `strict` (bool): 是否严格匹配 (默认 True)。若为 False，未知参数会收集到名为 `$extra` 的参数中。
*   `context_style` (Literal["bracket", "parentheses"] | None): 上下文插值风格 (`{...}` 或 `$(...)`)。
*   `raise_exception` (bool): 解析失败时是否抛出异常 (默认 False)。
*   `hide` (bool): 是否在管理器或帮助中隐藏此命令。
*   ...

#### 6. 命名空间 (Namespace)

用于管理 Alconna 的全局或分组默认设置。

**常用属性**:

*   `prefixes` (list): 默认命令前缀。
*   `separators` (tuple): 命令各部分之间的分隔符 (默认空格)。
*   `fuzzy_match`, `compact`, `strict`, `context_style`, `raise_exception` 等: 同 `CommandMeta`，作为默认值。
*   `builtin_option_name` (dict): 内置选项 (`help`, `shortcut`, `comp`) 的名称和别名。

**使用**:

*   创建: `ns = Namespace("my_ns", prefixes=["/"])`
*   应用: `Alconna(..., namespace=ns)`
*   上下文管理: `with namespace("my_ns") as ns: ns.prefixes = ["!"] ...` (在此 `with` 块内创建的 Alconna 默认使用此 ns)
*   修改默认: `config.default_namespace.prefixes = ["."]` 或 `config.default_namespace = new_ns`

#### 7. 快捷指令 (Shortcut)

允许用一个简单的指令 (或正则) 触发一个更复杂的命令，并传递参数。

**设置**: `alc.shortcut(key, args: ShortcutArgs)`

*   `key` (str): 快捷指令名称或正则。
*   `args` (dict):
    *   `command` (str): 要执行的完整命令模板。
    *   `args` (list): 将快捷指令参数按顺序填入原始命令 Args 的列表。
    *   `fuzzy` (bool): 是否允许快捷指令后跟额外参数 (默认 True)。
    *   `prefix` (bool): 执行时是否保留快捷指令的前缀。

**模板占位符**:

*   `{N}`: 正则匹配的第 N 个捕获组。
*   `{name}`: 正则匹配的命名捕获组 `(?P<name>...)`。
*   `{%N}`: 快捷指令后的第 N 个参数。
*   `{*}` 或 `{*sep}`: 快捷指令后的所有参数，可用 `sep` 指定分隔符。

**示例**:

```python
# 正则 + args
alc = Alconna("setu", Args["count", int])
alc.shortcut("涩图(\d+)张", {"args": ["{0}"]}) # {0} 是正则的第一个捕获组
# alc.parse("涩图3张").query("count") -> 3

# command 模板
alc = Alconna("eval", Args["content", str])
alc.shortcut("echo", {"command": "eval print(\'{*}\')"}) # {*} 是快捷指令后的所有参数
# alc.parse("echo hello world!") -> (执行 eval print('hello world!'))
```

**内置选项**: `--shortcut` 用于动态管理快捷指令 (`list`, `delete <key>`, `add <key> <command>`)。

#### 8. 紧凑命令 (Compact)

设置 `compact=True` 在 `Alconna`, `Option`, `Subcommand` 上，允许名称后直接跟参数。

```python
# gcc -Fabc -Fdef
alc = Alconna("gcc", Option("--flag|-F", Args["content", str], action=append, compact=True))
# alc.parse("gcc -Fabc -Fdef").query[list]("flag.content") -> ['abc', 'def']

# pp -vvv
alc = Alconna("pp", Option("--verbose|-v", action=count, default=0)) # count action 默认 compact
# alc.parse("pp -vvv").query[int]("verbose.value") -> 3
```

#### 9. 模糊匹配 (Fuzzy Match)

在 `CommandMeta` 中设置 `fuzzy_match=True`。当输入与命令名、选项名等不完全匹配但足够相似时，会提示可能的正确命令。

```python
alc = Alconna("test_fuzzy", meta=CommandMeta(fuzzy_match=True))
# alc.parse("test_fuzy") -> (提示 "Do you mean 'test_fuzzy'?")
```

#### 10. 半自动补全 (Completion)

通过内置选项 `--comp` (或 `-cp`, `?`) 触发，提示用户接下来可以输入的内容。

#### 11. Duplication

用于配合类型提示和 IDE 补全。通过继承 `Duplication` 并定义与 Alconna 结构对应的属性（使用 `OptionResult`, `SubcommandStub` 或直接标注类型）来实现。

```python
from arclet.alconna import Duplication, OptionResult, SubcommandStub, ArgsStub

class PipDup(Duplication):
    verbose: OptionResult # 对应 Option("--verbose")
    install: SubcommandStub # 对应 Subcommand("install")

# 或直接标注类型 (需 Alconna 结构简单)
class SimpleDup(Duplication):
    package: str
    file: Optional[str] = None
```
使用：`alc.parse(message, duplication=MyDup)`

#### 12. 上下文插值 (Context Interpolation)

在 `CommandMeta` 或 `Namespace` 中设置 `context_style="bracket"` (`{...}`) 或 `"parentheses"` (`$(...)`)。解析时，命令中符合格式的占位符会被 `parse` 方法传入的 `context` 字典中的值替换。

```python
alc = Alconna("test", Args["foo", int], meta=CommandMeta(context_style="parentheses"))
# alc.parse("test $(bar)", context={"bar": 123}) -> (Arparma 结果中 foo 的值为 123)
```

---

## `nonebot-plugin-alconna` 插件

本插件将强大的 `Alconna` 命令解析器集成到 NoneBot2 中。

### 安装

```shell
pip install nonebot-plugin-alconna
# 或
nb plugin install nonebot-plugin-alconna
```

### 响应器 `on_alconna`

提供 `on_alconna` 函数创建基于 Alconna 的事件响应器 (`AlconnaMatcher`)。

```python
from nonebot_plugin_alconna import on_alconna
from arclet.alconna import Alconna

# command: 可以是 Alconna 对象或描述命令的字符串
# skip_for_unmatch: 命令不匹配时是否跳过 (True) 或将错误放入结果 (False)
# auto_send_output: 是否自动发送 Alconna 的输出 (如帮助信息、错误提示) 并结束处理 (True)
# aliases: 命令别名
# comp_config: 补全会话配置
# extensions: 加载的 Alconna 扩展
# use_origin: 是否使用原始消息 (未经 to_me 等处理)
# use_cmd_start / use_cmd_sep: 是否使用 NoneBot 的 COMMAND_START / COMMAND_SEP
matcher = on_alconna(
    Alconna(...),
    skip_for_unmatch=True,
    auto_send_output=False,
    # ... 其他参数 ...
)
```

**`AlconnaMatcher` 扩展方法**:

*   `.assign(path, value=..., or_not=True)`: **条件控制**。仅当解析结果中 `path` 对应的值等于 `value` (或根据 `or_not` 判断) 时，才执行被装饰的 Handler。`path` 语法同 `Arparma.query`。
    ```python
    @matcher.assign("subcommand_name") # 当匹配到子命令 subcommand_name 时执行
    async def _(): ...

    @matcher.assign("option_name.param_name", 123) # 当选项 option_name 的参数 param_name 等于 123 时执行
    async def _(): ...
    ```
*   `.dispatch(path, value=..., or_not=True)`: 功能类似 `.assign`，但返回一个新的 `AlconnaMatcher` 实例，用于为命令的不同分支创建独立的 Matcher 组。
*   `.got_path(path, prompt=..., middleware=...)`: 类似 NoneBot 的 `.got()`，但用于获取 Alconna 命令中 `path` 对应的参数。如果参数缺失，会发送 `prompt` 并等待用户输入。`middleware` 可用于处理用户输入。
    ```python
    @matcher.got_path("option_name.param_name", prompt="请输入参数值")
    async def got_param(param_value: str): # 类型注解应与 Alconna 定义一致
        await matcher.send(f"收到了: {param_value}")
    ```
*   `.set_path_arg(key, value)`, `.get_path_arg(key)`: `got_path` 的辅助方法。
*   `.reject_path(path, prompt=...)`: `got_path` 对应的 `reject`。
*   `.send()`, `.reject()`, `.finish()` 等: 参数 `prompt` 支持 `UniMessage` 对象。

**`~` 路径语法**: 在 `.assign`, `.dispatch`, `.got_path`, `Query` 中，`~param_name` 会将 `~` 替换为当前 Handler/Matcher 所属的父级路径。

### 依赖注入 (DI)

插件提供了多种方式在 Handler 中获取 Alconna 的解析结果：

**1. 直接类型注解 (推荐)**:

```python
from arclet.alconna import Alconna, Arparma, Duplication
from nonebot_plugin_alconna import CommandResult, Match, Query

@matcher.handle()
async def my_handler(
    # 直接获取参数值 (若不存在则跳过此 handler)
    param1: str,
    param2: int,

    # 使用 Match 判断参数是否存在
    opt_param: Match[str],

    # 使用 Query 查询参数 (可指定路径和默认值)
    sub_param: Query[bool] = Query("subcommand_name.option_name.param_name", default=False),

    # 获取完整的 Arparma 对象
    arp: Arparma,

    # 获取 CommandResult (包含 Arparma 和可能的输出)
    result: CommandResult,

    # 获取 Duplication 对象 (如果 parse 时传入了)
    dup: MyDup, # MyDup 需要是 Duplication 的子类

    # 获取 Alconna 命令对象本身
    source: Alconna,
):
    if opt_param.available:
        print(f"Optional param value: {opt_param.result}")
    if sub_param.available:
        print(f"Queried param value: {sub_param.result}")
    # ...
```

**2. 依赖注入函数**:

*   `AlconnaResult()`: 返回 `CommandResult`。
*   `AlconnaMatches()`: 返回 `Arparma`。
*   `AlconnaDuplication()`: 返回 `Duplication`。
*   `AlconnaMatch(path, default=..., middleware=...)`: 返回 `Match[T]`。
*   `AlconnaQuery(path, default=..., middleware=...)`: 返回 `Query[T]`。
*   `AlconnaExecResult()`: 返回命令 `action` 的结果。
*   `AlconnaExtension(ext_type)`: 返回指定类型的 `Extension` 实例。

**3. `Annotated` 注解**:

*   `param: Annotated[Arparma, AlcMatches]`
*   `param: Annotated[CommandResult, AlcResult]`

**辅助模型**:

*   `CommandResult`: 包含 `source: Alconna`, `result: Arparma`, `output: str | None`。
*   `Match[T]`: 表示参数是否在 `arp.all_matched_args` 中。`.available` (bool), `.result` (T)。
*   `Query[T]`: 表示参数是否能通过 `arp.query(path)` 获取。`.available` (bool), `.result` (T)。

### 跨平台适配 (`uniseg`)

`nonebot-plugin-alconna.uniseg` 子模块提供了通用消息组件，实现跨平台的消息处理。

#### 1. 通用消息段 (Segment)

定义了一系列平台无关的消息段模型，如 `Text`, `At`, `Image`, `Audio`, `Video`, `File`, `Reply`, `Reference`, `Card` 等。

*   **跨平台接收**: 在 Alconna 的 `Args` 中使用通用消息段类型，可以匹配来自不同适配器的相似消息段。
    ```python
    from nonebot_plugin_alconna.uniseg import Image as UniImage
    # Args["img", UniImage] 可以匹配来自 OB11, OB12, Telegram 等适配器的图片消息
    alc = Alconna("cmd", Args["img", UniImage])
    ```
*   **`children` 属性**: 用于处理 Satori 等协议中的嵌套消息元素，提供兼容性。
*   **`select`/`select_first`**: 在 Alconna `Args` 中用于从嵌套元素中选择特定类型的子元素。

#### 2. 通用消息序列 (UniMessage)

类似 NoneBot 的 `Message`，但其元素是通用消息段 (`Segment`)。

**获取**:

*   通过 DI: `msg: UniMsg = UniMsg()` 或 `msg: Annotated[UniMessage, UniMsg()]`

**核心功能**:

*   **构造**:
    *   `UniMessage("text")`
    *   `UniMessage(At("user", "123"))`
    *   `UniMessage([Segment1, Segment2, ...])`
    *   **链式方法**: `UniMessage.text("Hello").at("user", "123").image(...)`
*   **拼接**: 支持 `+`, `+=`, `.append()`, `.extend()`。
*   **模板**: `UniMessage.template("Hello, {name}!")`
    *   **扩展控制符**: `"{:At(user, target)}"` 或 `"{:Image(url=link)}"`。
    *   **Matcher 内扩展**: `"{:At(user, $event.get_user_id())}"`, `"$message_id"`, `"$target"`。
*   **检查**: `Segment in msg`, `Type in msg`, `msg.has(Type)`, `msg.only(Type)`。
*   **提取文本**: `msg.extract_plain_text()`。
*   **遍历/过滤/索引/切片**: 行为类似 `list`，但支持按类型进行过滤、索引和切片。
    *   `msg[At]` (过滤), `msg[At, 0]` (索引), `msg[Text, 0:2]` (切片)。
    *   `msg.include(Type1, Type2)`, `msg.exclude(Type)`。
    *   `msg.index(Type)`, `msg.count(Type)`, `msg.get(Type, count)`。
*   **跨平台发送**:
    *   `await msg.export(bot)`: 将 `UniMessage` 转换为特定适配器的 `Message`。
    *   `receipt = await msg.send(target=..., bot=..., at_sender=..., reply_to=...)`: 发送消息并返回 `Receipt` 对象。`target` 可以是 `Event`, `Target` 对象，或省略以使用事件上下文。`bot` 可省略。
    *   `receipt.recall(delay=...)`, `receipt.edit(new_msg, ...)`: 撤回或编辑已发送的消息。
*   **获取上下文**:
    *   `UniMessage.get_target(event, bot)` -> `Target`
    *   `UniMessage.get_message_id(event, bot)` -> `str`
    *   DI: `target: MsgTarget = MsgTarget()`, `msg_id: MessageId = MessageId()`
*   **`Target` 对象**: 描述发送目标 (`id`, `parent_id`, `channel`, `private`, `source`, `self_id`, `selector` 等)。
*   **自定义消息段**: 通过 `custom_register` 和 `custom_handler` 扩展 `uniseg` 对特定平台非标准消息段的支持。

#### 3. 适配器标注

插件内置了对主流 NoneBot 适配器的 `MessageSegment` 的标注，可在 `Alconna` 中直接使用，位于 `nonebot_plugin_alconna.adapters.<adapter_name>`。

### 条件控制 (`.assign` / `.dispatch`)

使用 `.assign(path, value?, or_not?)` 或 `.dispatch(path, value?, or_not?)` 将 Handler 或 Matcher 绑定到 Alconna 解析结果的特定分支。

```python
pip_cmd = on_alconna(pip_alconna)

# 仅当匹配到 pip install xxx 时执行
@pip_cmd.assign("install")
async def handle_install(res: CommandResult): ...

# 仅当匹配到 pip list 时执行
@pip_cmd.assign("list")
async def handle_list(res: CommandResult): ...

# 为 pip install --upgrade 创建独立 Matcher
upgrade_matcher = pip_cmd.dispatch("install.upgrade")
@upgrade_matcher.handle()
async def handle_upgrade(...): ...
```

### 响应器创建装饰 (`funcommand`)

将一个普通函数快速转换为 Alconna 命令响应器。函数参数会自动映射为 Alconna `Args`。

```python
from nonebot_plugin_alconna import funcommand

@funcommand()
async def echo(msg: str): # -> Alconna("echo", Args["msg", str])
    return msg # 返回值会自动发送
```

### 类 Koishi 构造器 (`Command`)

提供链式调用的方式构建 `AlconnaMatcher`。

```python
from nonebot_plugin_alconna import Command

matcher = (
    Command("book", "命令描述")
    .usage("用法说明")
    .option("writer", "-w <id:int>") # 定义选项，<...> 内是 Args 定义
    .shortcut("快捷指令名", {"args": ["--anonymous"]}) # 定义快捷指令
    .action(lambda options: str(options)) # 定义处理函数 (可选)
    .build() # 构建 AlconnaMatcher
)
```

### 返回值中间件 (`middleware`)

在 `AlconnaMatch`, `AlconnaQuery`, `got_path` 中使用 `middleware` 参数，传入一个函数来处理获取到的原始值。

```python
from nonebot_plugin_alconna import image_fetch # 内置的图片获取中间件

@matcher.handle()
async def _(img_bytes: Match[bytes] = AlconnaMatch("img_arg", middleware=image_fetch)):
    if img_bytes.available:
        # 处理图片字节
        ...
```

### 匹配拓展 (Extension)

允许通过继承 `Extension` 类并实现其方法，来自定义 `AlconnaMatcher` 的行为。

**可覆盖方法**:

*   `validate`: 验证事件是否适用于此 Matcher。
*   `output_converter`: 自定义 Alconna 输出信息的格式。
*   `message_provider`: 自定义从事件中提取消息的方式。
*   `permission_check`: 在命令解析前进行权限检查。
*   `parse_wrapper`: 修改 Alconna 的解析结果。
*   `send_wrapper`: 修改发送出去的消息。
*   `catch`/`before_catch`: 自定义依赖注入。
*   ...

**使用**: `on_alconna(..., extensions=[MyExtension()])` 或通过配置全局加载。

**内置扩展**: `ReplyRecordExtension`, `DiscordSlashExtension`, `MarkdownOutputExtension`, `TelegramSlashExtension`。

### 补全会话 (Completion Session)

当命令参数缺失或错误时，提供交互式的参数补全引导。

**触发**:

*   在 `Args` 的 `Field` 中定义 `completion` 函数 (`Field(completion=lambda: "提示语")`)。
*   通过内置选项 `--comp` 手动触发。

**配置**: 通过 `on_alconna(..., comp_config={...})` 或全局配置 `alconna_auto_completion=True` 启用。`comp_config` 可设置提示词、超时、禁用特定指令等。

### 内置插件

*   `echo`: 简单的回显命令。
*   `help`: 显示所有已注册的 Alconna 命令列表（基于 Alconna 元数据）。

使用 `load_builtin_plugins("echo", "help")` 加载。

### 配置项

在 NoneBot 的配置文件 (`.env.*`) 中设置：

*   `alconna_auto_send_output` (bool, False): 全局自动发送 Alconna 输出。
*   `alconna_use_command_start` (bool, False): 使用 `COMMAND_START` 作为全局前缀。
*   `alconna_use_command_sep` (bool, False): 使用 `COMMAND_SEP` 作为全局分隔符。
*   `alconna_auto_completion` (bool, False): 全局启用补全会话。
*   `alconna_use_origin` (bool, False): 全局使用原始消息。
*   `alconna_global_extensions` (list[str], []): 全局加载的 Extension (点分隔路径)。
*   `alconna_context_style` (Literal | None, None): 全局上下文插值风格。
*   ... (其他配置项)

---

好的，我已经查阅了 Alconna 的官方文档 (`https://arclet.top/alconna/`)，并与之前整理的文档进行了对比。官方文档内容非常详尽，确实有一些额外的用法和概念可以补充进来，以使文档更加完善。

以下是根据官方文档补充和细化的内容：

---

**补充内容到 [Alconna 本体](#alconna-本体) 部分:**

**1. 参数声明(Args) -> default / Field 补充:**

*   **`Field` 的 `validators`**: 除了 `completion` (补全提示)，`Field` 还支持 `validators` 参数，可以传入一个或多个验证函数，对匹配到的参数值进行进一步检查或转换。
    ```python
    from arclet.alconna import Alconna, Args, Field

    def check_positive(x):
        if x <= 0:
            raise ValueError("必须是正数")
        return x

    alc = Alconna("cmd", Args["num", int, Field(validators=[check_positive])])
    # alc.parse("cmd 10") -> OK
    # alc.parse("cmd -5") -> 解析失败，触发 validator 异常
    ```

**2. 新增: 分隔符 (Separator)**

*   Alconna 使用分隔符来区分命令的不同部分（如选项和参数）。默认分隔符是空格 (`" "`)。
*   可以在 `Namespace` 或 `Alconna` 的构造函数中通过 `separators: tuple[str, ...]` 参数指定自定义的分隔符。
    ```python
    # 命令形式如: cmd=arg1|opt=arg2
    alc = Alconna("cmd", Args["arg1", str], Option("opt", Args["arg2", str]), separators="=|")
    ```

**3. 新增: `AllParam` 特殊参数**

*   `AllParam` 是 `Args` 中的一个特殊类型，用于捕获从它开始的所有剩余参数，并将它们作为一个列表（通常是字符串列表）。它必须是 `Args` 中的最后一个参数。
    ```python
    from arclet.alconna import Alconna, Args, AllParam

    alc = Alconna("say", Args["content", AllParam])
    # alc.parse("say hello world how are you").query("content") -> ['hello', 'world', 'how', 'are', 'you']
    ```

**4. 新增: `HeadMatcher` 自定义命令头匹配**

*   对于非常规的命令头（例如需要动态判断或复杂逻辑匹配），可以自定义 `HeadMatcher` 类并传入 `Alconna` 的构造函数，以实现自定义的命令头匹配逻辑。这是一个高级功能。

**5. 新增: 命令中间件 (Middleware)**

*   除了在依赖注入时使用中间件处理*解析后*的参数值，还可以在 `Alconna` 构造时传入 `middleware: Callable[[Message], Message | None]`。
*   这个中间件会在 Alconna **正式解析之前** 处理传入的原始消息，可以用于预处理、过滤、修改消息内容。如果中间件返回 `None`，则解析过程会中止。

**6. 新增: 解析钩子 (Parsing Hooks)**

*   Alconna 允许注册在解析过程特定阶段执行的钩子函数：
    *   `alc.hook(hook_func, hook_type)`
    *   `hook_type` 包括：
        *   `analyse_pre`: 解析开始前。
        *   `analyse_post`: 解析结束后（无论成功失败）。
        *   `analyse_fail`: 解析失败时。
        *   `analyse_suc`: 解析成功时。
*   这对于需要在解析前后执行特定逻辑（如日志、统计、修改结果）非常有用。

**7. 新增: 事件系统 (Event System)**

*   Alconna 内部有一个事件系统，允许监听和响应解析过程中的各种事件，如 `PreParse` (解析前), `PostParse` (解析后), `ExecSuccess` (执行器成功), `ExecFail` (执行器失败) 等。
*   可以通过 `alc.on(EventType, func)` 注册事件监听器。这是一个更底层、更灵活的控制方式。

**8. 新增: 执行器绑定 (`@alc.bind()`)**

*   除了通过 `nonebot-plugin-alconna` 的 Handler 处理，Alconna 本体也支持直接绑定一个函数作为命令执行器。
*   使用 `@alc.bind()` 装饰器，当命令解析成功时，Alconna 会自动调用这个绑定的函数，并将解析结果 `Arparma` 或其解包后的参数传入。
    ```python
    from arclet.alconna import Alconna, Args

    alc = Alconna("add", Args["a", int]["b", int])

    @alc.bind()
    def execute_add(a: int, b: int): # 参数名需要与 Args 中的 key 对应
        print(f"{a} + {b} = {a + b}")
        return a + b # 返回值可以通过 AlconnaExecResult 获取

    # alc.parse("add 1 2") 会自动调用 execute_add 并打印 "1 + 2 = 3"
    ```

**9. 新增: 命令管理器 (`alc_manager`)**

*   `arclet.alconna.manager` 提供了 `command_manager` (别名 `alc_manager`)，用于管理多个 Alconna 命令实例。
*   功能包括：注册/移除命令、根据名称/前缀获取命令、执行命令（自动查找匹配的命令并解析）等。
*   `alc_manager.add_command(alc)`
*   `alc_manager.get_command("cmd_name")`
*   `alc_manager.parse(message)`: 尝试用所有已注册的命令解析消息。

**10. 新增: 格式化器 (Formatters)**

*   Alconna 使用 Formatter 来生成帮助文本 (`--help` 的输出)。
*   内置格式化器：
    *   `TextFormatter` (默认): 纯文本格式。
    *   `MarkdownFormatter`: Markdown 格式。
    *   `ShellTextFormatter`: 模拟 Shell 帮助信息。
*   可以在 `Namespace` 或 `Alconna` 构造时通过 `formatter_type` 参数指定。

---

**补充内容到 `nonebot-plugin-alconna` 插件部分:**

**1. 依赖注入 (DI) -> 返回值中间件 (Middleware) 补充:**

*   明确 `middleware` 不仅限于内置的（如 `image_fetch`），可以是任何接受原始匹配值并返回处理后值的 `Callable` 或 `Awaitable`。

**2. 匹配拓展 (Extension) -> 可覆盖方法补充:**

*   补充官方文档中提到的其他可覆盖方法：
    *   `context_provider`: 对命令上下文的额外处理 (官方文档中提到，但在你给的 MD 中似乎没有)。
    *   **(你提供的 MD 中已包含)** `validate`, `output_converter`, `message_provider`, `receive_provider`, `permission_check`, `parse_wrapper`, `send_wrapper`, `before_catch`, `catch`, `post_init`。

**3. 补全会话 (Completion Session) 补充:**

*   更清晰地说明补全会话是基于 **Alconna 本体** 的半自动补全功能，并通过 `nonebot-plugin-alconna` 提供了交互式体验。
*   强调触发条件：参数缺失（需要 `Field(completion=...)` 定义提示）或手动触发（`--comp`）。
*   列出 `CompConfig` 的所有可用配置项（tab, enter, exit, timeout, hides, disables, lite）。

**4. 配置项 补充:**

*   列出所有在 `Alconna.md` 末尾提到的配置项，并简要说明其作用 (已在你提供的 MD 中，这里确认一下完整性)：
    *   `alconna_auto_send_output`
    *   `alconna_use_command_start`
    *   `alconna_use_command_sep`
    *   `alconna_auto_completion`
    *   `alconna_use_origin`
    *   `alconna_global_extensions`
    *   `alconna_context_style`
    *   `alconna_enable_saa_patch` (可能与 SAA 插件相关)
    *   `alconna_apply_filehost` (文件托管相关)
    *   `alconna_apply_fetch_targets` (启动时获取发送目标列表)

---