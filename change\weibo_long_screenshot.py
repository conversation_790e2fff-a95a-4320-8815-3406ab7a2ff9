#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博搜索页面长截图优化脚本
支持完整页面截图和搜索结果解析
"""

import time
from pathlib import Path
from datetime import datetime

WEIBO_COOKIE = "SCF=Avnel0_ySyS83UGbaVrwVGvmQ_WttQ2z-SayX-bJEHp97WhtFIlLc1po8XWjDgTwvK5mTsIVz8KJ7-jjSQ9InxA.; SINAGLOBAL=2796824338695.966.1746431339082; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9WhZeMY23Lc3WklJPfm_-2Db5JpX5KMhUgL.Fo-Neo5feKMRSKn2dJLoIEXLxKnL12BL1h.LxK-LBoML1-BLxK.LB.-L1K.LxKBLBo.L12zLxKBLB.zL12zt; ULV=1748416914037:5:5:2:3966026182495.447.1748416913989:1748402055626; XSRF-TOKEN=Osb1EyXeqte_snjonJbtte7W; ALF=1751612553; SUB=_2A25FO5_XDeRhGeNJ6VIU8SnEzjSIHXVmOJ0frDV8PUJbkNAbLRbmkW1NS_8Cb27w_amK1d_7Hep8LUBaJUhO5hWL; WBPSESS=G9Ff9BQjWfN2Zb8Vj7-gcwmvo0PYsN7wONuulMc9Q5oWncn4qAal3EhfCOJV886qLblUlqIv73HMsWvVNdv_E3vN-HHcP0heB3Jr6QVusbfYg5hZpyaPhLrCV5LcdQZ4f0Mpp8GKfq4smFGKnso0oA=="

TEST_URL = "https://s.weibo.com/weibo?q=%E8%9C%9C%E9%9B%AA%E5%86%B0%E5%9F%8E%E7%88%86%E5%8D%95%E5%A5%B3%E5%BA%97%E5%91%98%E5%BF%99%E5%88%B0%E5%B4%A9%E6%BA%83%E8%90%BD%E6%B3%AA"

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
    "Accept-Encoding": "gzip, deflate, br",
    "DNT": "1",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Cookie": WEIBO_COOKIE,
}


def optimize_screenshot_quality(screenshot_path):
    """优化截图质量"""
    try:
        from PIL import Image, ImageEnhance

        img = Image.open(screenshot_path)

        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.2)

        enhancer = ImageEnhance.Sharpness(img)
        img = enhancer.enhance(1.1)

        img.save(screenshot_path, "PNG", optimize=True, quality=95)

        print(f"截图质量已优化: {screenshot_path}")
        return True

    except ImportError:
        print("PIL未安装，跳过图片质量优化")
        return False
    except Exception as e:
        print(f"图片质量优化失败: {e}")
        return False


def capture_element_long_screenshot(driver, element, output_file):
    """捕获元素的长截图"""
    try:
        from PIL import Image
        import io

        element_height = driver.execute_script("return arguments[0].scrollHeight;", element)
        element_width = element.size["width"]
        viewport_height = driver.execute_script("return window.innerHeight;")

        print(f"元素完整高度: {element_height}px, 视口高度: {viewport_height}px")

        if element_height <= viewport_height:
            element.screenshot(str(output_file))
            return True

        screenshots = []
        scroll_position = 0
        segment_height = viewport_height - 100

        while scroll_position < element_height:
            driver.execute_script(f"arguments[0].scrollTop = {scroll_position};", element)
            time.sleep(0.5)

            screenshot_data = element.screenshot_as_png
            screenshot_image = Image.open(io.BytesIO(screenshot_data))
            screenshots.append(screenshot_image)

            scroll_position += segment_height

            if len(screenshots) > 20:
                break

        if not screenshots:
            return False

        total_height = sum(img.height for img in screenshots)
        combined_image = Image.new("RGB", (element_width, total_height))

        y_offset = 0
        for img in screenshots:
            combined_image.paste(img, (0, y_offset))
            y_offset += img.height

        combined_image.save(output_file)

        for img in screenshots:
            img.close()

        return True

    except ImportError:
        print("PIL未安装，无法进行元素长截图")
        return False
    except Exception as e:
        print(f"元素长截图失败: {e}")
        return False


def remove_unwanted_elements(driver):
    """移除页面中的干扰元素"""
    try:
        selectors_to_remove = [
            "#pl_feed_main > div.main-side",
            "body > div:nth-child(3) > div.m-main > div > div.m-main-nav",
            ".gn_nav",
            ".m-main-nav",
            ".main-side",
            ".gn_topmenulist",
            ".WB_frame_a",
            ".gn_header",
            ".m-box-center-a",
            '[class*="ad"]',
            '[class*="banner"]',
            ".WB_frame",
            ".gn_topmenu",
        ]

        removed_count = 0
        for selector in selectors_to_remove:
            try:
                js_code = f"""
                var elements = document.querySelectorAll('{selector}');
                var count = 0;
                elements.forEach(function(element) {{
                    if (element && element.parentNode) {{
                        element.parentNode.removeChild(element);
                        count++;
                    }}
                }});
                return count;
                """
                count = driver.execute_script(js_code)
                removed_count += count
                if count > 0:
                    print(f"移除了 {count} 个 '{selector}' 元素")

            except Exception as e:
                print(f"移除元素 '{selector}' 时出错: {e}")
                continue

        print(f"总共成功移除 {removed_count} 个干扰元素")

        time.sleep(1)

    except Exception as e:
        print(f"移除干扰元素失败: {e}")


def test_selenium_long_screenshot():
    """测试Selenium长截图"""
    print("=" * 60)
    print("测试Selenium长截图")
    print("=" * 60)

    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.common.action_chains import ActionChains

        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument(f"--user-agent={HEADERS['User-Agent']}")

        chrome_options.add_argument("--force-device-scale-factor=2")
        chrome_options.add_argument("--high-dpi-support=1")
        chrome_options.add_argument("--device-scale-factor=2")
        chrome_options.add_argument("--window-size=2800,2000")

        driver = webdriver.Chrome(options=chrome_options)

        try:
            driver.set_window_size(2800, 2000)

            print("设置Cookie...")
            driver.get("https://weibo.com")

            for cookie in WEIBO_COOKIE.split(";"):
                if "=" in cookie:
                    name, value = cookie.split("=", 1)
                    driver.add_cookie({"name": name.strip(), "value": value.strip(), "domain": ".weibo.com"})

            print(f"正在访问: {TEST_URL}")
            driver.get(TEST_URL)

            print("等待页面加载...")
            WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            time.sleep(3)

            print("滚动页面加载更多内容...")
            last_height = driver.execute_script("return document.body.scrollHeight")
            scroll_count = 0
            max_scrolls = 3

            while scroll_count < max_scrolls:
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

                time.sleep(2)

                new_height = driver.execute_script("return document.body.scrollHeight")

                if new_height == last_height:
                    break

                last_height = new_height
                scroll_count += 1
                print(f"完成第 {scroll_count} 次滚动")

            driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

            print("定位 #pl_feedlist_index 元素...")
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "#pl_feedlist_index"))
                )

                target_element = driver.find_element(By.CSS_SELECTOR, "#pl_feedlist_index")

                driver.execute_script("arguments[0].scrollIntoView(true);", target_element)
                time.sleep(1)

                element_location = target_element.location
                element_size = target_element.size

                print(f"元素位置: x={element_location['x']}, y={element_location['y']}")
                print(f"元素尺寸: width={element_size['width']}, height={element_size['height']}")

                total_height = element_location["y"] + element_size["height"] + 400

                print(f"调整窗口高度到: {total_height}px (2倍分辨率)")

                driver.set_window_size(2800, max(2000, total_height))
                time.sleep(2)

                driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(1)

                target_element = driver.find_element(By.CSS_SELECTOR, "#pl_feedlist_index")

                print("生成 #pl_feedlist_index 元素截图...")
                screenshot_file = Path(
                    f"weibo_feedlist_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                )

                target_element.screenshot(str(screenshot_file))
                print(f"元素截图已保存到: {screenshot_file.absolute()}")

                optimize_screenshot_quality(screenshot_file)

            except Exception as e:
                print(f"定位或截图 #pl_feedlist_index 元素失败: {e}")
                print("回退到全页面截图...")
                screenshot_file = Path(
                    f"weibo_full_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                )
                driver.save_screenshot(str(screenshot_file))
                print(f"全页面截图已保存到: {screenshot_file.absolute()}")

            print("解析搜索结果...")
            try:
                driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(1)

                try:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "#pl_feedlist_index"))
                    )
                    print("找到主要内容区域 #pl_feedlist_index")
                except:
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".card-wrap, .m-con-box, .card"))
                    )

                selectors = [
                    "#pl_feedlist_index .card-wrap",
                    "#pl_feedlist_index .m-con-box",
                    "#pl_feedlist_index .card",
                    ".card-wrap",
                    ".m-con-box",
                    ".card",
                    "[class*='card']",
                    ".weibo-text",
                ]

                result_cards = []
                for selector in selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            result_cards = elements
                            print(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                            break
                    except:
                        continue

                if result_cards:
                    print(f"找到 {len(result_cards)} 个搜索结果")

                    for i, card in enumerate(result_cards[:10], 1):
                        try:
                            text_content = card.text.strip()
                            if text_content and len(text_content) > 10:
                                print(f"结果 {i}: {text_content[:150]}...")
                                print("-" * 40)
                        except Exception as parse_e:
                            print(f"解析结果 {i} 失败: {parse_e}")
                else:
                    print("未找到搜索结果元素")

            except Exception as search_e:
                print(f"解析搜索结果失败: {search_e}")

            print(f"页面标题: {driver.title}")

            return True, screenshot_file, []

        finally:
            driver.quit()

    except ImportError:
        print("Selenium未安装，跳过截图测试")
        print("安装命令: pip install selenium")
        print("还需要下载ChromeDriver: https://chromedriver.chromium.org/")
        return False, None, []
    except Exception as e:
        print(f"Selenium截图失败: {e}")
        return False, None, []


def combine_screenshots(screenshots):
    """合并分段截图"""
    if not screenshots or len(screenshots) < 2:
        return None

    try:
        from PIL import Image

        print("合并分段截图...")

        images = []
        total_height = 0
        max_width = 0

        for screenshot in screenshots:
            if screenshot.exists():
                img = Image.open(screenshot)
                images.append(img)
                total_height += img.height
                max_width = max(max_width, img.width)

        if not images:
            print("没有有效的截图文件")
            return None

        combined = Image.new("RGB", (max_width, total_height))

        y_offset = 0
        for img in images:
            combined.paste(img, (0, y_offset))
            y_offset += img.height

        combined_file = Path(f"weibo_combined_screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        combined.save(combined_file)

        print(f"合并截图已保存到: {combined_file.absolute()}")

        for screenshot in screenshots:
            try:
                screenshot.unlink()
            except:
                pass

        return combined_file

    except ImportError:
        print("PIL未安装，无法合并截图")
        print("安装命令: pip install Pillow")
        return None
    except Exception as e:
        print(f"合并截图失败: {e}")
        return None


def main():
    """主函数"""
    print("微博搜索页面长截图测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试URL: {TEST_URL}")
    print()

    success, full_screenshot, _ = test_selenium_long_screenshot()

    if success:
        print("\n" + "=" * 60)
        print("截图测试完成")
        print("=" * 60)

        if full_screenshot and full_screenshot.exists():
            print(f"✅ 全页面截图: {full_screenshot.absolute()}")
    else:
        print("❌ 截图测试失败")

    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
