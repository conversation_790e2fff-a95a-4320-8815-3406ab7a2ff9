<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微博热搜榜</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "PingFang SC", "Microsoft YaHei", "Hiragino Sans GB", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }
        
        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .date {
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
            font-size: 14px;
            position: relative;
            z-index: 1;
        }
        
        .news-list {
            padding: 25px;
        }
        
        .news-item {
            background: white;
            border-radius: 15px;
            margin-bottom: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .news-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .news-item:last-child {
            margin-bottom: 0;
        }
        
        .news-rank {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            box-shadow: 0 4px 10px rgba(240, 147, 251, 0.3);
        }
        
        .news-rank.top1 {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            animation: pulse 2s infinite;
        }
        
        .news-rank.top2 {
            background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
            box-shadow: 0 4px 15px rgba(255, 167, 38, 0.4);
        }
        
        .news-rank.top3 {
            background: linear-gradient(135deg, #ffca28 0%, #ffa000 100%);
            box-shadow: 0 4px 15px rgba(255, 202, 40, 0.4);
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .news-item.top1 { border-left-color: #ff6b6b; }
        .news-item.top2 { border-left-color: #ffa726; }
        .news-item.top3 { border-left-color: #ffca28; }
        
        .news-content {
            flex: 1;
            min-width: 0;
        }
        
        .news-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
            line-height: 1.4;
            word-wrap: break-word;
        }
        
        .news-title .tag {
            display: inline-block;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 8px;
            font-weight: 500;
        }

        .news-title .tag.hot {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
        }

        .news-title .tag.new {
            background: linear-gradient(135deg, #ff3852 0%, #c44569 100%);
            box-shadow: 0 2px 6px rgba(255, 56, 82, 0.3);
        }

        .news-title .tag.variety {
            background: linear-gradient(135deg, #a55eea 0%, #8854d0 100%);
            box-shadow: 0 2px 6px rgba(165, 94, 234, 0.3);
        }

        .news-title .tag.default {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
        }
        
        .news-title .topic {
            color: #667eea;
            font-weight: 700;
        }
        
        .news-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .news-hot {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }
        
        .news-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 15px;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        
        .news-link:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            color: #7f8c8d;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        
        .stat-item {
            color: #95a5a6;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .news-list {
                padding: 15px;
            }
            
            .news-item {
                padding: 15px;
                margin-bottom: 12px;
            }
            
            .news-rank {
                width: 35px;
                height: 35px;
                font-size: 14px;
                margin-right: 15px;
            }
            
            .news-title {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 微博热搜榜</h1>
            <div class="subtitle">实时热门话题</div>
            <div class="date">{{ date }}</div>
        </div>
        <div class="news-list">
            {% for item in news_items %}
            <div class="news-item {% if item.index == 1 %}top1{% elif item.index == 2 %}top2{% elif item.index == 3 %}top3{% endif %}">
                <div class="news-rank {% if item.index == 1 %}top1{% elif item.index == 2 %}top2{% elif item.index == 3 %}top3{% endif %}">{{ item.index }}</div>
                <div class="news-content">
                    <div class="news-title">
                        {% if item.title.startswith('[') and ']' in item.title %}
                            {% set parts = item.title.split(']', 1) %}
                            {% set tag_text = parts[0][1:] %}
                            {% set tag_class = 'default' %}
                            {% if tag_text == '热' %}
                                {% set tag_class = 'hot' %}
                            {% elif tag_text == '新' %}
                                {% set tag_class = 'new' %}
                            {% elif tag_text == '综艺' %}
                                {% set tag_class = 'variety' %}
                            {% endif %}
                            <span class="tag {{ tag_class }}">{{ tag_text }}</span>{{ parts[1] }}
                        {% elif item.title.startswith('#') and item.title.endswith('#') %}
                            <span class="topic">{{ item.title }}</span>
                        {% else %}
                            {{ item.title }}
                        {% endif %}
                    </div>
                    <div class="news-meta">
                        <a href="{{ item.url }}" class="news-link">🔍 查看详情</a>
                        {% if item.hot %}
                        <span class="news-hot">🔥 {{ item.hot }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="footer">
            <div class="stats">
                <span class="stat-item">📊 共 {{ news_items|length }} 条热搜</span>
                <span class="stat-item">⏰ {{ update_time }}</span>
            </div>
            <div>数据来源：微博热搜榜 | 由多源日报插件生成</div>
        </div>
    </div>
</body>
</html>
