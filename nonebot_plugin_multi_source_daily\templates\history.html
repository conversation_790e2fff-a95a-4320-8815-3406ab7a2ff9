<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史上的今天</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "Hiragino Sans GB", sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0 0;
            font-size: 14px;
            opacity: 0.8;
        }
        .content {
            padding: 20px;
        }
        .history-item {
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .history-item h3 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .history-item p {
            margin: 0;
            line-height: 1.5;
            color: #555;
        }
        .footer {
            text-align: center;
            padding: 15px;
            font-size: 12px;
            color: #999;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ title }}</h1>
            <p>{{ date }}</p>
        </div>
        <div class="content">
            {% for item in news_items[:20] %}
            <div class="history-item">
                <h3>{{ item.title }}</h3>
                {% if item.description %}
                <p>{{ item.description }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        <div class="footer">
            历史上的今天 - 日报插件
        </div>
    </div>
</body>
</html>
