# 日报插件优化与功能扩充建议

## 1. 增加更多日报源

当前项目已经支持了60s日报、知乎日报、IT之家日报和摸鱼人日历，可以考虑增加以下日报源：

- **微博热搜日报**：获取当日微博热搜榜单
- **每日天气预报**：根据用户设置的城市提供天气预报
- **每日英语/单词**：提供每日英语学习内容
- **财经日报**：提供股市、基金等财经信息
- **游戏资讯日报**：提供游戏行业新闻和更新

## 2. 改进知乎日报API

目前知乎日报API使用的是示例URL，需要更新为实际可用的API：

```python
# 知乎日报API（示例，实际可能需要修改）
daily_news_zhihu_api: str = "https://api.example.com/zhihu_daily"
```

可以使用知乎日报的官方API或第三方API服务。

## 3. 增加用户个性化设置

- **个人订阅**：允许普通用户在私聊中订阅日报，而不仅限于群聊
- **自定义模板**：允许用户自定义日报的显示模板
- **内容过滤**：允许用户设置关键词过滤，只接收感兴趣的内容

## 4. 改进缓存机制

- **分布式缓存**：对于多机器人实例部署，可以考虑使用Redis等分布式缓存
- **预加载机制**：在高峰期前预加载热门日报内容，减少用户等待时间

## 5. 增加交互功能

- **反馈机制**：用户可以对日报内容进行点赞、收藏或评论
- **内容推荐**：基于用户历史交互推荐相关内容
- **订阅管理界面**：提供更友好的订阅管理界面，而不仅是命令行

## 6. 技术优化

- **异步优化**：优化API请求的并发处理，减少等待时间
- **错误处理**：增强错误处理和重试机制，提高稳定性
- **日志完善**：增加更详细的日志记录，便于问题排查

## 7. 新功能

- **日报搜索**：允许用户搜索历史日报内容
- **定制化摘要**：使用AI技术生成日报内容的摘要
- **多语言支持**：增加多语言支持，如英文、日文等
- **导出功能**：允许用户导出日报内容为PDF或其他格式
- **订阅提醒**：在日报发送前提醒用户即将收到日报

## 8. UI/UX改进

- **响应式设计**：优化HTML模板，使其在不同设备上显示更好
- **主题切换**：提供暗色/亮色主题切换
- **交互动画**：增加简单的交互动画，提升用户体验

## 9. 安全性增强

- **访问控制**：细化权限控制，区分管理员和普通用户权限
- **API密钥管理**：安全管理第三方API密钥
- **内容审核**：对获取的内容进行简单审核，过滤不适宜内容

## 10. 监控和统计

- **使用统计**：记录日报使用情况，生成统计报告
- **性能监控**：监控API响应时间和成功率
- **用户反馈分析**：收集和分析用户反馈，持续改进

## 实现示例

### 添加微博热搜日报

在 `api.py` 中添加新的函数来获取微博热搜：

```python
async def fetch_weibo_news(params: Dict[str, Any]) -> Message:
    """获取微博热搜日报"""
    from .data import news_cache
    from datetime import datetime
    
    api_type = params.get("format_type", "img")
    force_refresh = params.get("force_refresh", False)
    
    # 如果不是强制刷新，尝试从缓存获取
    if not force_refresh:
        cached_data = news_cache.get("weibo", api_type)
        if cached_data:
            logger.debug(f"从缓存获取微博热搜日报，格式: {api_type}")
            return cached_data
    
    # 缓存未命中或强制刷新，从API获取
    api_url = "https://weibo.com/ajax/side/hotSearch"
    
    try:
        response = await fetch_with_retry(
            api_url, 
            max_retries=config.daily_news_max_retries,
            timeout=config.daily_news_timeout
        )
        
        if response.status_code != 200:
            return Message(f"获取微博热搜日报失败，状态码：{response.status_code}")
        
        data = response.json()
        
        if api_type == "text":
            # 文本格式
            message = Message(f"【微博热搜榜 - {datetime.now().strftime('%Y年%m月%d日')}】\n\n")
            
            for i, item in enumerate(data.get("data", {}).get("realtime", [])[:20], 1):
                message.append(f"{i}. {item['note']} ({item['num']})\n")
            
            # 缓存结果
            news_cache.set("weibo", api_type, message, expire_time=1800)  # 30分钟过期
            return message
        
        elif api_type == "img":
            # 构建HTML内容
            from pathlib import Path
            import os
            
            # 确保模板目录存在
            template_path = Path(__file__).parent / "templates"
            os.makedirs(template_path, exist_ok=True)
            
            # 准备数据
            hot_items = []
            for item in data.get("data", {}).get("realtime", [])[:20]:
                hot_items.append({
                    "title": item["note"],
                    "hot": item["num"],
                    "url": f"https://s.weibo.com/weibo?q={item['note']}"
                })
            
            # 使用模板生成图片
            from nonebot_plugin_htmlrender import template_to_pic
            
            pic = await template_to_pic(
                template_path=str(template_path),
                template_name="weibo.html",
                templates={
                    "date": datetime.now().strftime("%Y年%m月%d日"),
                    "hot_items": hot_items
                },
                pages={
                    "viewport": {"width": 800, "height": 1000},
                    "base_url": f"file://{template_path}",
                },
            )
            
            message = Message(MessageSegment.image(pic))
            
            # 缓存结果
            news_cache.set("weibo", api_type, message, expire_time=1800)  # 30分钟过期
            return message
            
    except Exception as e:
        logger.error(f"获取微博热搜日报失败: {e}", exc_info=True)
        return Message(f"获取微博热搜日报失败: {str(e)}")
```

然后在 `__init__.py` 中注册这个新的日报源：

```python
register_news_source(
    name="微博",
    description="微博热搜榜",
    fetch_func=fetch_weibo_news,
    default_format="img",
    api_url="https://weibo.com/ajax/side/hotSearch"
)
```

### 添加个人订阅功能

在 `commands.py` 中添加新的命令处理函数，允许用户在私聊中订阅日报：

```python
# 个人日报订阅命令
personal_news_set = on_alconna(
    Alconna(
        "个人日报",
        Args["news_type", str],
        Args["time", f"re:{TIME_REGEX}"],
        Option("-f", Args["format_type", str]),
        meta=CommandMeta(
            compact=True,
            description="设置个人定时日报",
            usage="个人日报 [类型] [HH:MM或HHMM] [-f 格式]",
        ),
    ),
    priority=5,
    block=True,
)

@personal_news_set.handle()
async def handle_personal_news_set(
    bot: Bot,
    event: PrivateMessageEvent,
    matcher: AlconnaMatcher,
    res: CommandResult,
):
    # 获取解析结果
    arp = res.result
    
    # 获取日报类型
    news_type = arp.all_matched_args.get("news_type")
    if not news_type:
        await matcher.finish("请指定日报类型")
        return
    
    # 检查日报类型是否存在
    if news_type not in news_sources:
        await matcher.finish(f"未知的日报类型: {news_type}，可用类型：{', '.join(news_sources.keys())}")
        return
    
    # 获取日报源配置
    source = news_sources[news_type]
    
    # 解析参数
    time_str = arp.all_matched_args.get("time")
    
    format_type = arp.all_matched_args.get("format_type")
    if not format_type or format_type not in source.formats:
        format_type = source.default_format
    
    try:
        # 解析时间
        hour, minute = parse_time(time_str)
        
        # 验证时间有效性
        if not (0 <= hour < 24 and 0 <= minute < 60):
            await matcher.send(f"无效的时间: {time_str}，请使用HH:MM或HHMM格式")
            return
        
        # 为用户设置个人定时任务
        user_id = event.user_id
        add_personal_news_job(user_id, news_type, hour, minute, format_type)
        await matcher.send(f"已为您设置{news_type}日报，时间: {hour:02d}:{minute:02d}，格式: {format_type}")
        
    except ValueError as e:
        await matcher.send(f"设置定时任务失败: {str(e)}")
    except Exception as e:
        logger.error(f"设置定时任务失败: {e}", exc_info=True)
        await matcher.send(f"设置定时任务失败: {str(e)}")
```

### 改进错误处理和重试机制

可以增强 `fetch_with_retry` 函数，添加更智能的重试策略：

```python
async def fetch_with_retry(url: str, max_retries: int = 3, timeout: float = 10.0, 
                          headers: Dict = None, method: str = "GET", data: Any = None) -> httpx.Response:
    """带智能重试机制的HTTP请求函数"""
    retries = 0
    last_error = None
    retry_delay = 1.0  # 初始重试延迟
    
    while retries < max_retries:
        try:
            async with httpx.AsyncClient(follow_redirects=True) as client:
                if method.upper() == "GET":
                    response = await client.get(url, timeout=timeout, headers=headers)
                elif method.upper() == "POST":
                    response = await client.post(url, timeout=timeout, headers=headers, data=data)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                
                # 检查状态码，某些状态码可能需要重试
                if response.status_code in [429, 500, 502, 503, 504]:
                    retries += 1
                    logger.warning(f"服务器返回错误状态码 {response.status_code}，第{retries}次重试: {url}")
                    
                    # 如果响应中包含Retry-After头，使用它作为延迟时间
                    retry_after = response.headers.get("Retry-After")
                    if retry_after and retry_after.isdigit():
                        await asyncio.sleep(int(retry_after))
                    else:
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 1.5  # 指数退避
                    
                    continue
                
                return response
                
        except httpx.TimeoutException as e:
            last_error = e
            retries += 1
            logger.warning(f"请求超时，第{retries}次重试: {url}")
            await asyncio.sleep(retry_delay)
            retry_delay *= 1.5  # 指数退避
            
        except Exception as e:
            last_error = e
            retries += 1
            logger.warning(f"请求失败，第{retries}次重试: {url}, 错误: {str(e)}")
            await asyncio.sleep(retry_delay)
            retry_delay *= 1.5  # 指数退避
    
    # 所有重试都失败
    raise last_error or Exception(f"请求失败，已重试{max_retries}次")
```

### 添加使用统计功能

可以添加一个简单的统计模块，记录日报的使用情况：

```python
class UsageStats:
    """使用统计类"""
    def __init__(self):
        self.stats = {
            "total_requests": 0,
            "by_type": {},
            "by_group": {},
            "by_user": {},
            "errors": 0,
            "last_reset": time.time()
        }
    
    def record_request(self, news_type: str, group_id: Optional[int] = None, user_id: Optional[int] = None):
        """记录一次请求"""
        self.stats["total_requests"] += 1
        
        # 按类型统计
        if news_type not in self.stats["by_type"]:
            self.stats["by_type"][news_type] = 0
        self.stats["by_type"][news_type] += 1
        
        # 按群组统计
        if group_id:
            group_key = str(group_id)
            if group_key not in self.stats["by_group"]:
                self.stats["by_group"][group_key] = 0
            self.stats["by_group"][group_key] += 1
        
        # 按用户统计
        if user_id:
            user_key = str(user_id)
            if user_key not in self.stats["by_user"]:
                self.stats["by_user"][user_key] = 0
            self.stats["by_user"][user_key] += 1
    
    def record_error(self, news_type: str):
        """记录一次错误"""
        self.stats["errors"] += 1
        
        # 按类型统计错误
        if news_type not in self.stats["by_type"]:
            self.stats["by_type"][news_type] = {"requests": 0, "errors": 0}
        if "errors" not in self.stats["by_type"][news_type]:
            self.stats["by_type"][news_type]["errors"] = 0
        self.stats["by_type"][news_type]["errors"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计数据"""
        return self.stats
    
    def reset_stats(self):
        """重置统计数据"""
        self.stats = {
            "total_requests": 0,
            "by_type": {},
            "by_group": {},
            "by_user": {},
            "errors": 0,
            "last_reset": time.time()
        }

# 创建全局实例
usage_stats = UsageStats()
```

### 新功能详细说明

#### 1. 个人订阅功能

**功能描述**：允许用户在私聊中订阅日报，每天定时接收自己感兴趣的日报内容。

**用户交互**：
- 订阅：`个人日报 60s 08:00 -f img`
- 查看：`个人日报查看`
- 取消：`个人日报取消 60s`

#### 2. 日报搜索功能

**功能描述**：允许用户搜索历史日报内容，找到特定关键词相关的新闻或信息。

**用户交互**：
- 搜索：`日报搜索 [关键词] [-t 类型] [-d 天数]`
- 例如：`日报搜索 AI -t 60s -d 7`（搜索最近7天60s日报中关于AI的内容）

#### 3. 多语言支持

**功能描述**：支持多种语言的日报内容和界面，满足不同语言用户的需求。

**用户交互**：
- 设置语言：`日报语言 [zh/en/jp]`
- 使用特定语言：`日报 60s -l en`（获取英文版60s日报）

#### 4. 内容过滤功能

**功能描述**：允许用户设置关键词过滤，只接收包含或不包含特定关键词的内容。

**用户交互**：
- 设置包含关键词：`日报过滤 添加 -i 科技,AI`（只接收包含"科技"或"AI"的内容）
- 设置排除关键词：`日报过滤 添加 -e 娱乐,明星`（不接收包含"娱乐"或"明星"的内容）
- 查看过滤设置：`日报过滤 查看`
- 清除过滤设置：`日报过滤 清除`

#### 5. 定制化摘要功能

**功能描述**：使用AI技术为日报内容生成简短摘要，帮助用户快速了解重点信息。

**用户交互**：
- 获取摘要：`日报 60s -s`（获取60s日报的摘要版本）
- 设置摘要长度：`日报 60s -s 100`（生成100字的摘要）
