from nonebot import require

require("nonebot_plugin_htmlrender")

# 注意顺序，先require再 from ... import

# 注意顺序，先require再 from ... import

# 注意顺序，先require再 from ... import

import io

# 注意顺序，先require再 from ... import

# 注意顺序，先require再 from ... import

# 注意顺序，先require再 from ... import

from nonebot import on_command
from nonebot.adapters.onebot.v11 import Bot, MessageEvent, MessageSegment
from PIL import Image

from nonebot_plugin_htmlrender import (
    get_new_page,
    md_to_pic,
    template_to_pic,
    text_to_pic,
)

from .utils import count_to_color

# 纯文本转图片

text2pic = on_command("text2pic")

@text2pic.handle()
async def _text2pic(bot: Bot, event: MessageEvent):
    msg = str(event.get_message())

    # css_path 可选
    # from pathlib import Path
    # pic = await text_to_pic(
    #     text=msg, css_path=str(Path(__file__).parent / "templates" / "markdown.css")
    # )

    pic = await text_to_pic(text=msg)
    a = Image.open(io.BytesIO(pic))
    a.save("text2pic.png", format="PNG")
    await text2pic.finish(MessageSegment.image(pic))

# 加载本地 html 方法

html2pic = on_command("html2pic")

@html2pic.handle()
async def _html2pic(bot: Bot, event: MessageEvent):
    from pathlib import Path

    # html 可使用本地资源
    async with get_new_page(viewport={"width": 300, "height": 300}) as page:
        await page.goto(
            "file://" + (str(Path(__file__).parent / "html2pic.html")),
            wait_until="networkidle",
        )
        pic = await page.screenshot(full_page=True, path="./html2pic.png")

    await html2pic.finish(MessageSegment.image(pic))

# 使用 template2pic 加载模板

template2pic = on_command("template2pic")

@template2pic.handle()
async def _template2pic(bot: Bot, event: MessageEvent):
    from pathlib import Path

    text_list = ["1", "2", "3", "4"]
    template_path = str(Path(__file__).parent / "templates")
    template_name = "text.html"
    # 设置模板
    # 模板中本地资源地址需要相对于 base_url 或使用绝对路径
    pic = await template_to_pic(
        template_path=template_path,
        template_name=template_name,
        templates={"text_list": text_list},
        pages={
            "viewport": {"width": 600, "height": 300},
            "base_url": f"file://{template_path}",
        },
        wait=2,
    )

    a = Image.open(io.BytesIO(pic))
    a.save("template2pic.png", format="PNG")

    await template2pic.finish(MessageSegment.image(pic))

# 使用自定义过滤器

template_filter = on_command("template_filter")

@template_filter.handle()
async def_():
    from pathlib import Path

    count_list = ["1", "2", "3", "4"]
    template_path = str(Path(__file__).parent / "templates")
    template_name = "progress.html.jinja2"

    pic = await template_to_pic(
        template_path=template_path,
        template_name=template_name,
        templates={"counts": count_list},
        filters={"count_to_color": count_to_color},
        pages={
            "viewport": {"width": 600, "height": 300},
            "base_url": f"file://{template_path}",
        },
    )

    a = Image.open(io.BytesIO(pic))
    a.save("template_filter.png", format="PNG")

    await template_filter.finish(MessageSegment.image(pic))

# 使用 md2pic

md2pic = on_command("md2pic")

@md2pic.handle()
async def _md2pic(bot: Bot, event: MessageEvent):
    # from pathlib import Path

    # 如果是直接获取消息内容 需要 unescape
    from nonebot.adapters.onebot.v11 import unescape

    msg = unescape(str(event.get_message()))

    # css_path 可选
    # pic = await md_to_pic(
    #     md=msg, css_path=str(Path(__file__).parent / "templates" / "markdown.css")
    # )

    pic = await md_to_pic(md=msg)

    a = Image.open(io.BytesIO(pic))
    a.save("md2pic.png", format="PNG")

    await md2pic.finish(MessageSegment.image(pic))
md2pic <div align="center">
<h1>html格式支持和居中</h1>
<img width="250" src="https://v2.nonebot.dev/logo.png"/>
<div>
html格式 图片支持
</div>
</div>

# 一级标题

## 二级标题

### 三级标题

#### 四级标题

##### 五级标题

###### 六级标题

# 文本

*斜体文本*

*斜体文本*

**粗体文本**

**粗体文本**

***粗斜体文本***

***粗斜体文本***

<s>删除线</s>

~~删除线~~

<u>下划线</u>

~小号~字体

emoji 😀😃😄😁😆😅

列表

* 第一项
* 第二项
* 第三项

1. 第一项
2. 第二项
3. 第三项

任务列表

* [X] 第一项
* [ ] 第二项

# 嵌套

1. 第一项：
    * 第一项嵌套的第一个元素
    * 第一项嵌套的第二个元素
2. 第二项：
    * 第二项嵌套的第一个元素
    * 第二项嵌套的第二个元素

* [X] 任务 1
  * [X] 任务 A
  * [ ] 任务 B
    * [x] 任务 a
    * [ ] 任务 b
    * [x] 任务 c
  * [X] 任务 C
* [ ] 任务 2
* [ ] 任务 3

分割线
----

# 图片

* 必须指定宽度或大小 如 `250` 或 `100%`

```html
<img width="20%" src="https://v2.nonebot.dev/logo.png"/>
```

# html同款标签

如 <kbd>Ctrl</kbd>+<kbd>Alt</kbd>+<kbd>Del</kbd>

# 引用

> 最外层
> > 第一层嵌套
> > > 第二层嵌套

# 代码

```python
import this
```

行内代码 `print("nonebot")`

# 表格

| 左对齐 | 右对齐 | 居中对齐 |
| :-----| ----: | :----: |
| 单元格 | 单元格 | 单元格 |
| 单元格 | 单元格 | 单元格 |

# 数学公式

单行公式

$$(1+x)^\alpha =1+\alpha x +\frac{\alpha (\alpha -1}{2!} x^2+\cdots+\frac{\alpha (\alpha - 1)\cdots(\alpha - n+1)}{n!}x^n+o(x^n)$$

`$$...$$`

行内公式 $f'(x_0)=\lim_{x\rightarrow x_0} \frac{f(x)-f(x_0)}{\Delta x}$ 行内公式

`$...$`

# 不支持

* md 格式图片插入（必须使用html格式）

* 某些符号会被自动转换
